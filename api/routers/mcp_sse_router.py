"""
MCP SSE router.
"""

import json
import logging
from contextlib import asynccontextmanager
from typing import List, Dict, Any, Optional, Tuple

from fastapi import APIRouter, Depends, Request as FastAPIRequest
from fastapi.responses import Response, StreamingResponse
from mcp.server.lowlevel import Server as McpServer
from mcp.server.sse import SseServerTransport
from mcp.types import TextContent
from mcp.types import Tool as McpTool
from sqlalchemy.ext.asyncio import AsyncSession

from api.database import get_db
from api.errors.mcp_error import McpMessageHandlingError, McpToolExecutionError
from api.errors.tool_error import ToolNotFoundError
from api.services.tool_service import ToolService

# Create logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["mcp-sse"])


class McpSSEServerManager:
    """MCP SSE 服务器管理类，负责初始化和管理 MCP 服务器及 SSE 传输"""

    def __init__(self):
        self.server: Optional[McpServer] = None
        self.transport: Optional[SseServerTransport] = None

    def initialize(self):
        """初始化 MCP 服务器和 SSE 传输

        注意：工具列表和执行函数将在 handle_sse 中注册，
        以便能够访问数据库会话
        """
        logger.info("初始化 MCP SSE 服务器...")
        self.server = McpServer("Easy MCP Server")
        self.transport = SseServerTransport("/messages/")

        # 注意：工具列表和执行函数将在 handle_sse 中注册
        # 这里不注册是因为我们需要数据库会话

        logger.info("MCP SSE 服务器初始化成功")

    def shutdown(self):
        """关闭 MCP 服务器"""
        logger.info("关闭 MCP SSE 服务器...")
        # 这里可以添加任何必要的清理代码
        self.server = None
        self.transport = None

    async def _get_enabled_tools(
        self, db: AsyncSession, tag_filter: Optional[str] = None
    ) -> List[McpTool]:
        """获取所有启用的工具并转换为 MCP 工具格式

        Args:
            db: 数据库会话
            tag_filter: 标签过滤器，如果提供则只返回包含该标签的工具

        Returns:
            List[McpTool]: MCP 工具列表
        """
        tool_service = ToolService(db)

        # 如果有标签过滤，先获取该标签的ID
        tag_ids = None
        if tag_filter:
            # 这里假设tag_filter是标签名称，需要转换为ID
            # 你也可以根据需要修改为直接传递标签ID
            from api.services.tag_service import TagService

            tag_service = TagService(db)
            tags, _ = await tag_service.query_tags(page=1, size=1000, search=tag_filter)
            if tags:
                # 找到匹配的标签
                matching_tag = next(
                    (tag for tag in tags if tag.name == tag_filter), None
                )
                if matching_tag:
                    tag_ids = [matching_tag.id]

        tools = await tool_service.query_tools(page=1, size=1000, tag_ids=tag_ids)

        # 获取所有工具（第一个元素是工具列表，第二个是总数）
        all_tools = tools[0]

        # 过滤启用的工具
        enabled_tools = [tool for tool in all_tools if tool.is_enabled]

        # 转换为 MCP 工具对象
        mcp_tools = []
        for tool in enabled_tools:
            mcp_tool = self._convert_to_mcp_tool(tool)
            if mcp_tool:
                mcp_tools.append(mcp_tool)

        logger.info(f"获取到 {len(mcp_tools)} 个启用的工具")
        return mcp_tools

    def _convert_to_mcp_tool(self, tool) -> Optional[McpTool]:
        """将数据库工具对象转换为 MCP 工具对象

        Args:
            tool: 数据库工具对象

        Returns:
            Optional[McpTool]: MCP 工具对象，如果转换失败则返回 None
        """
        try:
            # 解析参数 JSON Schema
            parameters = json.loads(tool.parameters) if tool.parameters else {}

            return McpTool(
                name=tool.name,
                description=tool.description or "",
                inputSchema=parameters,
            )
        except Exception as e:
            logger.error(f"转换工具 {tool.name} 时出错: {e}")
            return None

    async def _execute_tool(
        self, name: str, arguments: Dict[str, Any], db: AsyncSession
    ) -> List[TextContent]:
        """执行指定的工具

        Args:
            name: 工具名称
            arguments: 工具参数
            db: 数据库会话

        Returns:
            List[TextContent]: 执行结果

        Raises:
            ToolNotFoundError: 工具未找到
            McpToolExecutionError: 工具执行错误
        """
        tool_service = ToolService(db)

        # 获取工具
        tool = await tool_service.get_tool_by_name(name)
        if not tool:
            raise ToolNotFoundError(name=name)

        if not tool.is_enabled:
            raise McpToolExecutionError(
                f"工具 '{name}' 已禁用，无法执行"
            )

        # 执行工具
        result, errors = await self._process_tool_execution(name, arguments, db)

        # 格式化结果
        formatted_result = self._format_result(result)

        # 返回结果
        return [TextContent(type="text", text=formatted_result)]

    async def _process_tool_execution(
        self, name: str, arguments: Dict[str, Any], db: AsyncSession
    ) -> Tuple[Any, List[str]]:
        """处理工具执行逻辑

        Args:
            name: 工具名称
            arguments: 工具参数
            db: 数据库会话

        Returns:
            Tuple[Any, List[str]]: 执行结果和错误列表
        """
        tool_service = ToolService(db)

        # 获取工具
        tool = await tool_service.get_tool_by_name(name)
        if not tool:
            return None, [f"工具 '{name}' 未找到"]

        # 执行工具代码
        try:
            # 这里应该调用工具服务的执行方法
            # 具体实现取决于工具服务的接口
            result = await tool_service.execute_tool(tool, arguments)
            return result, []
        except Exception as e:
            logger.error(f"执行工具 {name} 时出错: {e}")
            return None, [str(e)]

    def _format_result(self, result: Any) -> str:
        """格式化执行结果

        Args:
            result: 执行结果

        Returns:
            str: 格式化后的结果字符串
        """
        if result is None:
            return "执行完成，无返回结果"

        if isinstance(result, str):
            return result

        try:
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception:
            return str(result)


# 全局 MCP 服务器管理器实例
_mcp_sse_server_manager = McpSSEServerManager()


@asynccontextmanager
async def mcp_server_lifespan():
    """MCP 服务器生命周期管理器"""
    try:
        _mcp_sse_server_manager.initialize()
        yield
    finally:
        _mcp_sse_server_manager.shutdown()


def get_mcp_server():
    """获取 MCP 服务器实例

    Returns:
        McpServer: MCP 服务器实例
    """
    return _mcp_sse_server_manager.server


def get_sse_transport():
    """获取 SSE 传输实例

    Returns:
        SseServerTransport: SSE 传输实例
    """
    return _mcp_sse_server_manager.transport


async def handle_sse(
    request: FastAPIRequest,
    db: AsyncSession = Depends(get_db),
    tag_filter: Optional[str] = None,
):
    """处理 SSE 连接

    Args:
        request: FastAPI 请求对象
        db: 数据库会话
        tag_filter: 标签过滤器

    Yields:
        str: SSE 消息
    """
    server = get_mcp_server()
    transport = get_sse_transport()

    if not server or not transport:
        logger.error("MCP 服务器或传输未初始化")
        return

    # 注册工具列表函数
    async def list_tools_with_db():
        return await _mcp_sse_server_manager._get_enabled_tools(db, tag_filter)

    # 注册工具执行函数
    async def execute_tool_with_db(name: str, arguments: Dict[str, Any]):
        return await _mcp_sse_server_manager._execute_tool(name, arguments, db)

    # 注册函数到服务器
    server.list_tools = list_tools_with_db
    server.execute_tool = execute_tool_with_db

    # 处理 SSE 连接
    async for message in transport.handle_request(request):
        yield message


@router.get("/sse")
async def sse_endpoint(request: FastAPIRequest, db: AsyncSession = Depends(get_db)):
    """SSE 端点，提供所有工具的 MCP 服务

    Args:
        request: FastAPI 请求对象
        db: 数据库会话

    Returns:
        StreamingResponse: SSE 响应
    """
    return StreamingResponse(
        handle_sse(request, db),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )


@router.get("/sse-{tag}")
async def sse_tag_endpoint(
    tag: str, request: FastAPIRequest, db: AsyncSession = Depends(get_db)
):
    """带标签过滤的 SSE 端点，只提供指定标签的工具

    Args:
        tag: 标签名称
        request: FastAPI 请求对象
        db: 数据库会话

    Returns:
        StreamingResponse: SSE 响应
    """
    return StreamingResponse(
        handle_sse(request, db, tag_filter=tag),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )


@router.route("/messages/{path:path}", methods=["POST"])
async def message_handler(request: FastAPIRequest):
    """处理 MCP 消息

    Args:
        request: FastAPI 请求对象

    Returns:
        Response: 响应
    """
    server = get_mcp_server()
    transport = get_sse_transport()

    if not server or not transport:
        logger.error("MCP 服务器或传输未初始化")
        return Response(status_code=500, content="Server not initialized")

    try:
        # 处理消息
        response = await transport.handle_message(request, server)
        return response
    except Exception as e:
        logger.error(f"处理 MCP 消息时出错: {e}")
        return Response(status_code=500, content=str(e)) 